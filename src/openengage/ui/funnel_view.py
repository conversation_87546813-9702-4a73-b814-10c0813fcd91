"""
Funnel View UI component for OpenEngage.

This module provides a comprehensive funnel visualization and campaign generation
system with three distinct sub-screens:
1. Overview screen with mass generation capability
2. Stage-specific details screen  
3. Individual journey builder screen
"""
import os
import json
import glob
import pandas as pd
import streamlit as st
import streamlit.components.v1
import plotly.graph_objects as go
import plotly.express as px
from datetime import datetime
from typing import Dict, List, Optional, Any
from pathlib import Path
from streamlit_plotly_events import plotly_events

# Import core functionality
from utils.file_utils import load_feature_toggles, get_all_organizations
from core.mass_email_generator import process_mass_email_data
from core.journey_builder import create_journey_tree_plotly
from ui.journey import display_journey_builder


def get_organization_name_from_session():
    """Get the current organization name from session state."""
    if hasattr(st.session_state, 'current_user') and st.session_state.current_user:
        org_data = st.session_state.current_user.get('organization', {})
        return org_data.get('name', 'Analytics Vidhya')
    elif hasattr(st.session_state, 'organization_name'):
        return st.session_state.organization_name
    else:
        return 'Analytics Vidhya'


def detect_organization_csv_files():
    """
    Detect and load organization-specific CSV files from Sample Data folder.

    Returns:
        tuple: (organization_name, csv_files_dict, fallback_used)
    """
    sample_data_dir = "Sample Data For Mass Generation"

    # Get current organization name
    org_name = get_organization_name_from_session()

    # Look for organization-specific processed user data file
    funnel_files = {}
    fallback_used = False

    if os.path.exists(sample_data_dir):
        # Get all CSV files in the directory
        csv_files = [f for f in os.listdir(sample_data_dir) if f.endswith('.csv')]

        # Look for organization-specific processed user data file first
        # Format: {Organization}_processed_user_data.csv (with underscore for spaces)
        org_file_name = f"{org_name.replace(' ', '_')}_processed_user_data.csv"
        org_file_path = os.path.join(sample_data_dir, org_file_name)

        if os.path.exists(org_file_path):
            # Use organization-specific file
            funnel_files['processed_user_data'] = org_file_path
        else:
            # Look for any funnel files as fallback
            fallback_files = [f for f in csv_files if 'funnel_' in f]
            if fallback_files:
                fallback_used = True
                for file in fallback_files:
                    # Extract stage name from filename
                    # Format: funnel_STAGE_NAME_TIMESTAMP.csv
                    parts = file.replace('.csv', '').split('_')
                    if len(parts) >= 3:
                        stage_name = '_'.join(parts[1:-1])  # Everything between 'funnel_' and timestamp
                        funnel_files[stage_name] = os.path.join(sample_data_dir, file)
            else:
                # Final fallback to default processed_user_data.csv
                default_file = os.path.join(sample_data_dir, "processed_user_data.csv")
                if os.path.exists(default_file):
                    fallback_used = True
                    funnel_files['processed_user_data'] = default_file

    return org_name, funnel_files, fallback_used


def load_funnel_data(csv_files_dict):
    """
    Load and process funnel data from CSV files.

    Args:
        csv_files_dict: Dictionary mapping stage names to CSV file paths

    Returns:
        dict: Dictionary mapping stage names to DataFrames
    """
    funnel_data = {}

    for stage_name, file_path in csv_files_dict.items():
        try:
            if os.path.exists(file_path):
                # Check if this is a processed_user_data file or funnel file
                if 'processed_user_data' in stage_name:
                    # For processed user data files, use different columns
                    use_cols = ['user_email', 'first_name', 'user_behaviour', 'user_stage']

                    # Load CSV with specific columns for performance
                    df = pd.read_csv(file_path, usecols=lambda x: x in use_cols)

                    # Group by user_stage to create funnel stages
                    if 'user_stage' in df.columns and not df.empty:
                        # Filter out rows with missing essential data
                        df = df.dropna(subset=['user_email', 'first_name'])
                        df = df[df['user_stage'].notna()]

                        # Group by stage and create separate DataFrames
                        for stage, stage_df in df.groupby('user_stage'):
                            if not stage_df.empty:
                                funnel_data[stage] = stage_df.reset_index(drop=True)
                    else:
                        # If no user_stage column, treat as single stage
                        df = df.dropna(subset=['user_email', 'first_name'])
                        if not df.empty:
                            funnel_data['All_Users'] = df
                else:
                    # For funnel files, use original columns
                    use_cols = ['user_email', 'first_name', 'user_behaviour', 'user_stage',
                                'Subject', 'Mail_Content', 'Matched_Product', 'Template_Name']

                    # Load CSV with specific columns for performance
                    df = pd.read_csv(file_path, usecols=lambda x: x in use_cols)

                    # Filter out rows with errors or empty content
                    df = df.dropna(subset=['user_email', 'first_name'])
                    df = df[df['user_stage'].notna()]

                    if not df.empty:
                        funnel_data[stage_name] = df
            else:
                st.warning(f"File not found: {file_path}")
        except Exception as e:
            st.error(f"Error loading {file_path}: {str(e)}")

    return funnel_data


def create_funnel_visualization(funnel_data):
    """
    Create a funnel visualization showing user progression through stages.

    Args:
        funnel_data: Dictionary mapping stage names to DataFrames

    Returns:
        plotly.graph_objects.Figure: Funnel chart
    """
    if not funnel_data:
        return None

    # Calculate stage metrics
    stage_counts = {}
    stage_names = []

    # Define a logical order for common stages
    stage_order = [
        'New Visitor', 'Product Page Viewed', 'Product Lead Generated',
        'Product Purchased', 'campaigns', 'All_Users'
    ]

    # Sort stages by the defined order, with unknown stages at the end
    sorted_stages = []
    for stage in stage_order:
        if stage in funnel_data:
            sorted_stages.append(stage)

    # Add any remaining stages not in the predefined order
    for stage_name in funnel_data.keys():
        if stage_name not in sorted_stages:
            sorted_stages.append(stage_name)

    for stage_name in sorted_stages:
        df = funnel_data[stage_name]
        # Count unique users in each stage
        unique_users = df['user_email'].nunique() if not df.empty else 0
        stage_counts[stage_name] = unique_users
        stage_names.append(stage_name.replace('_', ' ').title())

    # Create funnel chart
    fig = go.Figure()

    # Add funnel trace
    fig.add_trace(go.Funnel(
        y=stage_names,
        x=list(stage_counts.values()),
        textinfo="value+percent initial",
        textfont=dict(size=14),
        marker=dict(
            color=['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98FB98'],
            line=dict(width=2, color='white')
        ),
        connector=dict(line=dict(color='rgb(63, 63, 63)', dash='dot', width=3))
    ))

    # Update layout
    fig.update_layout(
        title={
            'text': 'User Journey Funnel',
            'x': 0.5,
            'xanchor': 'center',
            'font': {'size': 20}
        },
        font=dict(size=12),
        margin=dict(l=20, r=20, t=60, b=20),
        height=400
    )

    return fig


def display_funnel_overview(funnel_data, org_name, fallback_used):
    """Display the main funnel overview screen."""
    st.write("## 📊 Funnel View - Overview")

    # Show organization info
    if fallback_used:
        st.warning(f"⚠️ No organization-specific data found for '{org_name}'. Using default funnel data.")
    else:
        st.success(f"✅ Showing organization-specific funnel data for: **{org_name}**")

    # Generate Mass Campaign button at the top
    if funnel_data:
        st.write("### 🚀 Generate Mass Campaign")
        col1, col2 = st.columns([3, 1])
        with col1:
            st.write("Generate campaigns for users across all funnel stages")
            st.write("• Randomly selects up to 3 users from each stage")
            st.write("• Uses gpt-4o-mini model consistently")
            st.write("• Saves results to Sample Data For Mass Generation folder")

        with col2:
            if st.button("🎯 Generate Mass Campaign", type="primary", use_container_width=True):
                generate_mass_funnel_campaign(funnel_data)

        st.write("---")

    # Display funnel visualization
    if funnel_data:
        fig = create_funnel_visualization(funnel_data)
        if fig:
            st.plotly_chart(fig, use_container_width=True)

        # Enhanced Stage Summary with dropdown
        st.write("### 📋 Stage Summary")

        # Create summary table
        summary_data = []
        for stage_name, df in funnel_data.items():
            if not df.empty:
                unique_users = df['user_email'].nunique()
                total_records = len(df)
                # Check if Subject and Mail_Content columns exist (for funnel files vs processed user data)
                if 'Subject' in df.columns and 'Mail_Content' in df.columns:
                    has_content = len(df[df['Subject'].notna() & df['Mail_Content'].notna()])
                else:
                    has_content = 0

                summary_data.append({
                    'Stage': stage_name.replace('_', ' ').title(),
                    'Unique Users': unique_users,
                    'Total Records': total_records,
                    'Generated Content': has_content,
                    'Completion Rate': f"{(has_content/total_records*100):.1f}%" if total_records > 0 else "0%"
                })

        if summary_data:
            summary_df = pd.DataFrame(summary_data)
            st.dataframe(summary_df, use_container_width=True)

            # Stage selection dropdown and user display
            st.write("---")
            st.write("### 👥 Stage User Details")

            stage_options = list(funnel_data.keys())
            selected_stage = st.selectbox(
                "Select a stage to view generated users:",
                stage_options,
                format_func=lambda x: x.replace('_', ' ').title(),
                key="overview_stage_selector"
            )

            if selected_stage:
                display_stage_users_from_csv(selected_stage, org_name)

                # User Journey section
                st.write("---")
                st.write("### 🛤️ User Journey")
                display_user_journey_section(selected_stage, org_name)
    else:
        st.warning("No funnel data available. Please ensure CSV files are present in the Sample Data folder.")


def process_funnel_users_async(data_df, progress_callback=None, max_workers=5):
    """
    Process funnel users using asynchronous parallel processing with gpt-4o-mini.

    Args:
        data_df (pd.DataFrame): DataFrame containing user data
        progress_callback (callable, optional): Function to report progress
        max_workers (int): Maximum number of parallel workers

    Returns:
        pd.DataFrame: DataFrame with generated email content
    """
    from concurrent.futures import ThreadPoolExecutor, as_completed
    from core.product_selector import select_product_for_user
    from utils.file_utils import get_all_products, load_communication_settings
    from openai import OpenAI
    import time

    # Initialize OpenAI client
    client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))

    # Load products
    products = get_all_products()
    if not products:
        st.error("No products found. Please add products first.")
        return data_df

    total_users = len(data_df)
    results = []
    processed_count = 0

    def process_single_funnel_user(user_data):
        """Process a single user for email generation using gpt-4o-mini."""
        try:
            # Convert Series to dict if needed
            if hasattr(user_data, 'to_dict'):
                user_row = user_data.to_dict()
            else:
                user_row = user_data

            # Match product for user
            matched_product, similarity = select_product_for_user(user_row, products)

            # Load communication settings
            org_url = matched_product.get('Company_URL', '') or matched_product.get('organization_url', '')
            comm_settings = load_communication_settings(organization_url=org_url)

            # Create prompt for gpt-4o-mini
            prompt = f"""
            Generate a personalized email for the following user:

            User Details:
            - Name: {user_row.get('first_name', 'User')}
            - Email: {user_row.get('user_email', '')}
            - Stage: {user_row.get('user_stage', 'New')}
            - Behavior: {user_row.get('user_behaviour', 'No behavior data')}

            Product Details:
            - Product Name: {matched_product.get('Product_Name', '')}
            - Description: {matched_product.get('Product_Description', '')}
            - Features: {matched_product.get('Product_Features', '')}

            Communication Settings:
            - Tone: {comm_settings.get('tone', 'professional')}
            - Style: {comm_settings.get('style', 'friendly')}
            - Sender: {matched_product.get('Company_Name', 'OpenEngage Team')}

            Generate a JSON response with:
            {{
                "subject": "Email subject line",
                "content": "Email body content",
                "preheader": "Email preheader text"
            }}

            Make the email personalized, engaging, and relevant to the user's stage and behavior.
            """

            # Call gpt-4o-mini
            response = client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[
                    {"role": "system", "content": "You are an expert email marketing specialist. Generate personalized email content in JSON format."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.7,
                response_format={"type": "json_object"}
            )

            # Parse response
            import json
            email_data = json.loads(response.choices[0].message.content)

            # Generate HTML content
            from core.email_formatter import text_to_html
            email_content = {
                'subject': email_data.get('subject', ''),
                'content': email_data.get('content', ''),
                'preheader': email_data.get('preheader', '')
            }

            html_content = text_to_html(
                email_content,
                product_url=matched_product.get('Product_URL'),
                product_name=matched_product.get('Product_Name'),
                recipient_email=user_row.get('user_email'),
                recipient_first_name=user_row.get('first_name'),
                communication_settings=comm_settings
            )

            return {
                'user_email': user_row.get('user_email', ''),
                'first_name': user_row.get('first_name', ''),
                'user_behaviour': user_row.get('user_behaviour', ''),
                'user_stage': user_row.get('user_stage', ''),
                'Subject': email_data.get('subject', ''),
                'Mail_Content': email_data.get('content', ''),
                'Preheader': email_data.get('preheader', ''),
                'HTML_Content': html_content,
                'Matched_Product': matched_product.get('Product_Name', ''),
                'Similarity_Score': round(float(similarity) * 100, 2),
                'Template_Name': 'AI_Generated_gpt4o_mini',
                'success': True
            }

        except Exception as e:
            return {
                'user_email': user_data.get('user_email', ''),
                'first_name': user_data.get('first_name', ''),
                'user_behaviour': user_data.get('user_behaviour', ''),
                'user_stage': user_data.get('user_stage', ''),
                'Subject': f'Error: {str(e)}',
                'Mail_Content': 'Error generating content',
                'Preheader': '',
                'HTML_Content': '',
                'Matched_Product': '',
                'Similarity_Score': 0,
                'Template_Name': 'Error',
                'success': False,
                'error': str(e)
            }

    # Process users in parallel using ThreadPoolExecutor
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # Submit all tasks
        future_to_user = {
            executor.submit(process_single_funnel_user, row): row
            for _, row in data_df.iterrows()
        }

        # Collect results as they complete
        for future in as_completed(future_to_user):
            try:
                result = future.result()
                results.append(result)
                processed_count += 1

                # Report progress
                if progress_callback:
                    progress_callback(processed_count - 1, total_users, f"Generated email for {result['first_name']}")

                # Add small delay to respect API rate limits
                time.sleep(0.1)

            except Exception as e:
                user_data = future_to_user[future]
                results.append({
                    'user_email': user_data.get('user_email', ''),
                    'first_name': user_data.get('first_name', ''),
                    'user_behaviour': user_data.get('user_behaviour', ''),
                    'user_stage': user_data.get('user_stage', ''),
                    'Subject': f'Error: {str(e)}',
                    'Mail_Content': 'Error generating content',
                    'Preheader': '',
                    'HTML_Content': '',
                    'Matched_Product': '',
                    'Similarity_Score': 0,
                    'Template_Name': 'Error',
                    'success': False,
                    'error': str(e)
                })
                processed_count += 1

    # Convert results to DataFrame
    result_df = pd.DataFrame(results)

    # Final progress update
    if progress_callback:
        progress_callback(total_users, total_users, "Async generation completed!")

    return result_df


def display_stage_users_from_csv(selected_stage, org_name):
    """Display users from generated CSV files for the selected stage."""
    import glob

    # Look for CSV files in Sample Data For Mass Generation folder
    csv_pattern = "Sample Data For Mass Generation/funnel_mass_campaign_*.csv"
    csv_files = glob.glob(csv_pattern)

    if not csv_files:
        st.info("No generated campaign data found. Please generate a mass campaign first.")
        return

    # Get the most recent CSV file
    latest_csv = max(csv_files, key=os.path.getctime)

    try:
        # Load the CSV file
        df = pd.read_csv(latest_csv)

        # Filter by selected stage
        stage_users = df[df['user_stage'] == selected_stage]

        if stage_users.empty:
            st.warning(f"No users found for stage '{selected_stage}' in the generated campaign data.")
            return

        # Display up to 3 users
        users_to_display = stage_users.head(3)

        st.write(f"**Generated users for {selected_stage.replace('_', ' ').title()} stage:**")
        st.write(f"*Data from: {os.path.basename(latest_csv)}*")

        # Create columns for parallel display
        num_users = len(users_to_display)
        if num_users == 1:
            cols = [st.columns(1)[0]]
        elif num_users == 2:
            cols = st.columns(2)
        else:
            cols = st.columns(3)

        for idx, (_, user_row) in enumerate(users_to_display.iterrows()):
            if idx >= len(cols):
                break

            with cols[idx]:
                display_csv_user_details(user_row, idx + 1)

    except Exception as e:
        st.error(f"Error loading campaign data: {str(e)}")


def display_csv_user_details(user_row, user_number):
    """Display details for a single user from CSV data."""
    # User card container
    with st.container():
        st.markdown(f"""
        <div style="
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
            background-color: #f8f9fa;
        ">
        """, unsafe_allow_html=True)

        # User details
        st.markdown(f"**👤 User {user_number}**")
        st.markdown(f"**Name:** {user_row.get('first_name', 'N/A')}")
        st.markdown(f"**Email:** {user_row.get('user_email', 'N/A')}")
        st.markdown(f"**Stage:** {user_row.get('user_stage', 'N/A')}")

        # User behavior
        st.markdown("**🎯 Behavior:**")
        behavior = user_row.get('user_behaviour', 'No behavior data available')
        st.markdown(f"_{behavior}_")

        # Email content
        st.markdown("**📧 Email Subject:**")
        st.markdown(f"_{user_row.get('Subject', 'No subject')}_")

        # Template info if available
        if 'Template_Name' in user_row and user_row['Template_Name']:
            st.markdown(f"**📋 Template:** {user_row['Template_Name']}")

        # HTML Email Preview
        if 'HTML_Content' in user_row and user_row['HTML_Content']:
            st.markdown("**📱 Email Preview:**")
            with st.expander("View HTML Email", expanded=False):
                display_html_email_preview(user_row['HTML_Content'])
        elif 'Mail_Content' in user_row and user_row['Mail_Content']:
            # Generate HTML from text content if HTML not available
            st.markdown("**📱 Email Preview:**")
            with st.expander("View Email Content", expanded=False):
                # Import required modules for HTML generation
                from core.email_formatter import text_to_html
                from utils.file_utils import load_communication_settings

                # Create email content structure
                email_content = {
                    'subject': user_row.get('Subject', ''),
                    'content': user_row.get('Mail_Content', ''),
                    'preheader': user_row.get('Preheader', '')
                }

                # Generate HTML
                html_content = text_to_html(
                    email_content,
                    recipient_email=user_row.get('user_email'),
                    recipient_first_name=user_row.get('first_name'),
                    communication_settings=load_communication_settings()
                )

                if html_content:
                    display_html_email_preview(html_content)
                else:
                    st.text_area("Email Content", user_row.get('Mail_Content', ''), height=200, disabled=True)

        st.markdown("</div>", unsafe_allow_html=True)


def display_user_journey_section(selected_stage, org_name):
    """Display user journey section with dropdown to select users."""
    import glob

    # Look for CSV files in Sample Data For Mass Generation folder
    csv_pattern = "Sample Data For Mass Generation/funnel_mass_campaign_*.csv"
    csv_files = glob.glob(csv_pattern)

    if not csv_files:
        st.info("No generated campaign data found. Please generate a mass campaign first to see user journeys.")
        return

    # Get the most recent CSV file
    latest_csv = max(csv_files, key=os.path.getctime)

    try:
        # Load the CSV file
        df = pd.read_csv(latest_csv)

        # Filter by selected stage
        stage_users = df[df['user_stage'] == selected_stage]

        if stage_users.empty:
            st.warning(f"No users found for stage '{selected_stage}' in the generated campaign data.")
            return

        # Get up to 3 users
        users_to_show = stage_users.head(3)

        # Create user options for dropdown
        user_options = []
        for idx, (_, user_row) in enumerate(users_to_show.iterrows()):
            user_label = f"User {idx + 1}: {user_row.get('first_name', 'Unknown')} ({user_row.get('user_email', 'No email')})"
            user_options.append((user_label, user_row))

        if not user_options:
            st.warning("No users available for journey building.")
            return

        # User selection dropdown with None option
        user_option_labels = ["None - Select a user"] + [option[0] for option in user_options]
        selected_user_label = st.selectbox(
            "Select a user to build their journey:",
            user_option_labels,
            key="journey_user_selector"
        )

        # Find selected user data
        selected_user_data = None
        if selected_user_label != "None - Select a user":
            for label, user_data in user_options:
                if label == selected_user_label:
                    selected_user_data = user_data
                    break

        if selected_user_data is not None:
            # Display journey builder for selected user
            display_journey_builder_for_user(selected_user_data, selected_stage)

    except Exception as e:
        st.error(f"Error loading user journey data: {str(e)}")


def display_journey_builder_for_user(user_data, stage_name):
    """Display journey builder functionality for a specific user."""
    from utils.file_utils import get_all_products, load_user_journey
    from core.journey_builder import find_similar_product, create_journey_tree_plotly, handle_node_click
    from core.email_generator import generate_tree_emails

    st.write(f"**Building journey for:** {user_data.get('first_name', 'User')}")

    # Display user info
    col1, col2 = st.columns(2)
    with col1:
        st.markdown("**👤 User Details:**")
        st.markdown(f"- **Name:** {user_data.get('first_name', 'N/A')}")
        st.markdown(f"- **Email:** {user_data.get('user_email', 'N/A')}")
        st.markdown(f"- **Stage:** {user_data.get('user_stage', 'N/A')}")

    with col2:
        st.markdown("**🎯 User Behavior:**")
        behavior = user_data.get('user_behaviour', 'No behavior data available')
        st.markdown(f"_{behavior}_")

    # Load products and find matching product
    all_products = get_all_products()
    if not all_products:
        st.error("No products found. Please add products first.")
        return

    # Find matching product for user
    user_behavior = user_data.get('user_behaviour', '')
    matched_product, similarity = find_similar_product(user_behavior, all_products)

    if not matched_product:
        st.error("Could not find a matching product for this user.")
        return

    # Display matched product
    st.write("### 🎯 Matched Product")
    st.write(f"Based on the user's behavior: **{matched_product.get('Product_Name')}** (Similarity: {similarity:.2f})")

    # Load user journey stages
    product_name = matched_product.get('Product_Name', '')
    stages = load_user_journey(product_name)
    if not stages:
        st.error(f"No user journey stages found for product '{product_name}'. Please configure user journey first.")
        return

    # Debug information
    st.write(f"**Available stages:** {stages}")
    st.write(f"**Current user stage:** {current_stage} (type: {type(current_stage)})")

    # Find starting stage index
    current_stage = user_data.get('user_stage', stage_name)
    start_idx = 0  # Default to first stage

    # Ensure current_stage is a string and find matching stage
    if isinstance(current_stage, str) and stages:
        try:
            start_idx = next(i for i, stage in enumerate(stages) if isinstance(stage, str) and stage.lower() == current_stage.lower())
        except StopIteration:
            # If no exact match found, try partial matching
            try:
                start_idx = next(i for i, stage in enumerate(stages) if isinstance(stage, str) and current_stage.lower() in stage.lower())
            except StopIteration:
                start_idx = 0  # Default to first stage if no match found

    # Generation mode (simplified to use gpt-4o-mini)
    generation_mode = "template"  # Use template mode with gpt-4o-mini

    # Initialize journey state for this user
    journey_key = f"journey_{user_data.get('user_email', 'unknown')}"

    if f"{journey_key}_generated" not in st.session_state:
        st.session_state[f"{journey_key}_generated"] = False

    # Generate journey button
    if st.button(f"🚀 Generate Journey for {user_data.get('first_name', 'User')}",
                 key=f"generate_journey_{user_data.get('user_email', 'unknown')}",
                 type="primary"):

        # Set up session state for this user's journey
        st.session_state.journey_user_name = user_data.get('first_name', 'User')
        st.session_state.journey_user_email = user_data.get('user_email', '')
        st.session_state.journey_user_behavior = user_data.get('user_behaviour', 'No behavior data')
        st.session_state.journey_user_stage = user_data.get('user_stage', current_stage)
        st.session_state.matched_product = matched_product

        # Initialize journey steps
        st.session_state.journey_steps = []
        st.session_state.journey_steps.append({
            'type': 'stage',
            'action': stages[start_idx] if start_idx < len(stages) else stages[0],
            'timestamp': datetime.now().isoformat()
        })

        with st.expander("📧 **Generating personalized emails for journey stages**", expanded=True):
            st.markdown("Creating tailored email content using gpt-4o-mini...")

            # Save journey state
            st.session_state[f"{journey_key}_generated"] = True
            st.session_state.journey_stages = stages
            st.session_state.current_stage_idx = start_idx
            st.session_state.tree_start_idx = start_idx

            # Show progress bar
            progress_bar = st.progress(0)

            # Generate emails with progress updates
            current_stage_name = stages[start_idx] if start_idx < len(stages) else stages[0]
            generate_tree_emails(current_stage_name, 0, 0, matched_product, progress_bar, start_idx, max_level=2, generation_mode=generation_mode)
            progress_bar.progress(100)

            st.success("✓ Journey emails generated successfully!")

        st.rerun()

    # Display generated journey if available
    if st.session_state.get(f"{journey_key}_generated", False):
        display_user_journey_tree(stages, start_idx, matched_product, user_data)


def display_user_journey_tree(stages, start_idx, matched_product, user_data):
    """Display the journey tree and email content for a specific user."""
    from core.journey_builder import create_journey_tree_plotly, handle_node_click, create_user_journey_flow

    st.write("### 🌳 Journey Tree with Emails")
    st.write("Click on any node to view the personalized email content.")

    # Create journey tree
    fig, node_ids = create_journey_tree_plotly(stages, start_idx, st.session_state.get("traversed_node", 1))

    # Create columns for tree and email
    tree_col, email_col = st.columns([2, 1])

    with tree_col:
        # Display interactive tree and capture clicks
        clicked_data = plotly_events(fig, click_event=True, override_height=600,
                                   key=f"journey_tree_{user_data.get('user_email', 'unknown')}")

        if clicked_data and isinstance(clicked_data, list) and len(clicked_data) > 0:
            clicked_item = clicked_data[0]
            node_number = clicked_item.get("pointNumber", 0)
            st.session_state["selected_node"] = node_number + 1
            st.session_state["traversed_node"] = node_number + 1

    with email_col:
        # Check if we have a selected node
        if "selected_node" in st.session_state:
            handle_node_click(f"node_{st.session_state['selected_node']}")

    # Show success message
    st.success("Journey tree generated! Click a node to view email content.")


def display_funnel_view():
    """Main funnel view display function with sub-screen navigation."""
    
    # Initialize sub-screen state
    if 'funnel_view_screen' not in st.session_state:
        st.session_state.funnel_view_screen = 'overview'
    
    if 'selected_stage' not in st.session_state:
        st.session_state.selected_stage = None
    
    # Load funnel data
    org_name, csv_files, fallback_used = detect_organization_csv_files()
    funnel_data = load_funnel_data(csv_files)
    
    # Navigation tabs
    tab1, tab2, tab3 = st.tabs(["📊 Overview", "🔍 Stage Details", "🛠️ Journey Builder"])
    
    with tab1:
        display_funnel_overview(funnel_data, org_name, fallback_used)
    
    with tab2:
        display_stage_details(funnel_data)
    
    with tab3:
        display_individual_journey_builder()


def generate_mass_funnel_campaign(funnel_data):
    """Generate mass campaigns for funnel users."""
    if not funnel_data:
        st.error("No funnel data available for campaign generation.")
        return

    # Collect users from all stages (randomly select max 3 per stage)
    selected_users = []
    import random

    for stage_name, df in funnel_data.items():
        if not df.empty:
            # Randomly select up to 3 users from each stage
            num_users = min(3, len(df))
            if num_users > 0:
                # Randomly sample users
                random_users = df.sample(n=num_users, random_state=42).copy()
                # Add stage information if not present
                if 'user_stage' not in random_users.columns:
                    random_users['user_stage'] = stage_name
                selected_users.append(random_users)

    if not selected_users:
        st.warning("No users found for campaign generation.")
        return

    # Combine all selected users
    combined_df = pd.concat(selected_users, ignore_index=True)

    if combined_df.empty:
        st.warning("No users selected for campaign generation.")
        return

    st.write(f"### 🔄 Processing {len(combined_df)} users across {len(funnel_data)} stages")

    # Create progress tracking
    progress_container = st.container()
    with progress_container:
        progress_bar = st.progress(0)
        status_text = st.empty()

        def update_progress(current, total, message):
            progress = int((current / total) * 100) if total > 0 else 0
            progress_bar.progress(progress)
            status_text.text(f"{message} ({current}/{total})")

        # Process the campaign using existing mass email generator
        try:
            with st.spinner("Generating personalized campaigns..."):
                result_df = process_funnel_users_async(
                    combined_df,
                    progress_callback=update_progress
                )

            progress_bar.progress(100)
            status_text.text("Campaign generation completed!")

            # Display results
            st.success(f"✅ Successfully generated campaigns for {len(result_df)} users!")

            # Show sample results
            if not result_df.empty:
                st.write("### 📧 Sample Generated Content")

                # Display first few results
                display_cols = ['first_name', 'user_stage', 'Subject', 'Template_Name']
                available_cols = [col for col in display_cols if col in result_df.columns]
                display_df = result_df[available_cols].head(5)
                st.dataframe(display_df, use_container_width=True)

                # Save results with timestamp
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                output_file = f"Sample Data For Mass Generation/funnel_mass_campaign_{timestamp}.csv"
                result_df.to_csv(output_file, index=False)
                st.info(f"💾 Results saved to: {output_file}")

                # Show instruction to view generated content
                st.success("✅ You can now view the generated content by selecting a stage in the dropdown above.")

        except Exception as e:
            st.error(f"Error generating campaigns: {str(e)}")
            progress_bar.progress(0)
            status_text.text("Campaign generation failed.")


def display_stage_details(funnel_data):
    """Display stage-specific details screen."""
    st.write("## 🔍 Stage Details")

    if not funnel_data:
        st.warning("No funnel data available.")
        return

    # Stage selection
    stage_options = list(funnel_data.keys())
    if stage_options:
        selected_stage = st.selectbox(
            "Select a stage to analyze:",
            stage_options,
            format_func=lambda x: x.replace('_', ' ').title()
        )

        if selected_stage and selected_stage in funnel_data:
            df = funnel_data[selected_stage]

            if not df.empty:
                # Stage metrics
                col1, col2, col3, col4 = st.columns(4)

                with col1:
                    st.metric("Total Users", len(df))

                with col2:
                    unique_users = df['user_email'].nunique()
                    st.metric("Unique Users", unique_users)

                with col3:
                    # Check if content columns exist
                    if 'Subject' in df.columns and 'Mail_Content' in df.columns:
                        generated_content = len(df[df['Subject'].notna() & df['Mail_Content'].notna()])
                    else:
                        generated_content = 0
                    st.metric("Generated Content", generated_content)

                with col4:
                    completion_rate = (generated_content / len(df) * 100) if len(df) > 0 else 0
                    st.metric("Completion Rate", f"{completion_rate:.1f}%")

                # Generate campaign for this stage if no content exists
                if generated_content == 0:
                    st.write("---")
                    col1, col2 = st.columns([3, 1])

                    with col1:
                        st.write(f"### 🎯 Generate Campaign for {selected_stage.replace('_', ' ').title()}")
                        st.write(f"Generate campaigns for up to 3 users in this stage")

                    with col2:
                        if st.button(f"Generate for Stage", type="primary", use_container_width=True):
                            generate_stage_specific_campaign(selected_stage, df)
                else:
                    # Display the 3 users with generated content in parallel
                    display_stage_users_parallel(selected_stage, df)
            else:
                st.info(f"No data available for {selected_stage.replace('_', ' ').title()} stage.")
    else:
        st.info("No stages available in the funnel data.")


def display_stage_users_parallel(stage_name, stage_df):
    """Display 3 users from the stage in parallel with their details, behavior, and HTML emails."""
    st.write("---")
    st.write(f"### 👥 Users in {stage_name.replace('_', ' ').title()} Stage")

    # Get users with generated content (up to 3)
    users_with_content = stage_df[
        stage_df['Subject'].notna() &
        stage_df['Mail_Content'].notna()
    ].head(3)

    if users_with_content.empty:
        st.warning("No users with generated content found in this stage.")
        return

    # Create columns for parallel display
    num_users = len(users_with_content)
    if num_users == 1:
        cols = [st.columns(1)[0]]
    elif num_users == 2:
        cols = st.columns(2)
    else:
        cols = st.columns(3)

    for idx, (_, user_row) in enumerate(users_with_content.iterrows()):
        if idx >= len(cols):
            break

        with cols[idx]:
            display_single_user_details(user_row, stage_name, idx + 1)


def display_single_user_details(user_row, stage_name, user_number):
    """Display details for a single user including Build Journey button."""
    # User card container
    with st.container():
        st.markdown(f"""
        <div style="
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
            background-color: #f8f9fa;
        ">
        """, unsafe_allow_html=True)

        # Build Journey button at the top
        if st.button(
            f"🛠️ Build Journey",
            key=f"build_journey_{stage_name}_{user_number}",
            type="primary",
            use_container_width=True
        ):
            build_journey_for_user(user_row, stage_name)

        st.markdown("---")

        # User details
        st.markdown(f"**👤 User {user_number}**")
        st.markdown(f"**Name:** {user_row.get('first_name', 'N/A')}")
        st.markdown(f"**Email:** {user_row.get('user_email', 'N/A')}")
        st.markdown(f"**Stage:** {user_row.get('user_stage', 'N/A')}")

        # User behavior
        st.markdown("**🎯 Behavior:**")
        behavior = user_row.get('user_behaviour', 'No behavior data available')
        st.markdown(f"_{behavior}_")

        # Email content
        st.markdown("**📧 Email Subject:**")
        st.markdown(f"_{user_row.get('Subject', 'No subject')}_")

        # Template info if available
        if 'Template_Name' in user_row and user_row['Template_Name']:
            st.markdown(f"**📋 Template:** {user_row['Template_Name']}")

        # HTML Email Preview
        if 'HTML_Content' in user_row and user_row['HTML_Content']:
            st.markdown("**📱 Email Preview:**")
            with st.expander("View HTML Email", expanded=False):
                display_html_email_preview(user_row['HTML_Content'])
        elif 'Mail_Content' in user_row and user_row['Mail_Content']:
            # Generate HTML from text content if HTML not available
            st.markdown("**📱 Email Preview:**")
            with st.expander("View Email Content", expanded=False):
                # Import required modules for HTML generation
                from core.email_formatter import text_to_html
                from utils.file_utils import load_communication_settings

                # Create email content structure
                email_content = {
                    'subject': user_row.get('Subject', ''),
                    'content': user_row.get('Mail_Content', ''),
                    'preheader': user_row.get('Preheader', '')
                }

                # Generate HTML
                html_content = text_to_html(
                    email_content,
                    recipient_email=user_row.get('user_email'),
                    recipient_first_name=user_row.get('first_name'),
                    communication_settings=load_communication_settings()
                )

                if html_content:
                    display_html_email_preview(html_content)
                else:
                    st.text_area("Email Content", user_row.get('Mail_Content', ''), height=200, disabled=True)

        st.markdown("</div>", unsafe_allow_html=True)


def display_html_email_preview(html_content):
    """Display HTML email content in a preview container."""
    if not html_content:
        st.warning("No HTML content available")
        return

    # Create full HTML document for proper rendering
    full_html = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <style>
            body {{
                margin: 0;
                padding: 10px;
                font-family: Arial, sans-serif;
                background-color: white;
            }}
            img {{ max-width: 100%; height: auto; }}
            table {{ width: 100%; border-collapse: collapse; }}
            td {{ padding: 8px; }}
        </style>
    </head>
    <body>
        {html_content}
    </body>
    </html>
    """

    # Display using st.components.v1.html for proper rendering
    st.components.v1.html(
        full_html,
        height=400,
        scrolling=True
    )


def build_journey_for_user(user_row, stage_name):
    """Build individual journey for a specific user."""
    # Set up session state for journey builder
    st.session_state.journey_user_name = user_row.get('first_name', 'User')
    st.session_state.journey_user_email = user_row.get('user_email', '')
    st.session_state.journey_user_behavior = user_row.get('user_behaviour', 'No behavior data')
    st.session_state.journey_user_stage = user_row.get('user_stage', stage_name)

    # Reset journey state
    st.session_state.journey_generated = False
    st.session_state.journey_steps = []
    st.session_state.current_stage_idx = 0
    st.session_state.highlighted_node = 1
    st.session_state.traversed_node = 1
    st.session_state.selected_node = 1

    # Set funnel journey mode
    st.session_state.funnel_journey_mode = True
    st.session_state.funnel_journey_user = user_row
    st.session_state.funnel_journey_stage = stage_name

    st.success(f"🚀 Building Journey for {user_row.get('first_name', 'User')}...")
    st.rerun()


def generate_stage_specific_campaign(stage_name, stage_df):
    """Generate campaign for a specific stage."""
    if stage_df.empty:
        st.warning("No users available in this stage.")
        return

    # Select up to 3 users for generation
    available_users = stage_df.head(3).copy()

    st.write(f"### 🔄 Processing {len(available_users)} users from {stage_name.replace('_', ' ').title()}")

    # Create progress tracking
    progress_container = st.container()
    with progress_container:
        progress_bar = st.progress(0)
        status_text = st.empty()

        def update_progress(current, total, message):
            progress = int((current / total) * 100) if total > 0 else 0
            progress_bar.progress(progress)
            status_text.text(f"{message} ({current}/{total})")

        try:
            with st.spinner("Generating campaigns for stage users..."):
                result_df = process_mass_email_data(
                    available_users,
                    progress_callback=update_progress,
                    use_batch_api=True
                )

            progress_bar.progress(100)
            status_text.text("Stage campaign generation completed!")

            # Display results
            st.success(f"✅ Successfully generated campaigns for {len(result_df)} users in {stage_name.replace('_', ' ').title()}!")

            # Update the original funnel data with generated content
            if not result_df.empty:
                # Update the funnel data in session state if it exists
                if hasattr(st.session_state, 'funnel_data_cache'):
                    # Update the cached data
                    for idx, row in result_df.iterrows():
                        user_email = row.get('user_email')
                        if user_email:
                            # Find matching row in original data and update
                            mask = stage_df['user_email'] == user_email
                            if mask.any():
                                # Update the original dataframe
                                for col in ['Subject', 'Mail_Content', 'HTML_Content', 'Template_Name', 'Preheader']:
                                    if col in result_df.columns:
                                        stage_df.loc[mask, col] = row.get(col)

                # Save results to file
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                output_file = f"Sample Data For Mass Generation/funnel_{stage_name}_{timestamp}.csv"
                result_df.to_csv(output_file, index=False)
                st.info(f"💾 Results saved to: {output_file}")

                # Show success message and refresh instruction
                st.info("🔄 Please refresh the page or re-select the stage to see the generated content in the user details view.")

        except Exception as e:
            st.error(f"Error generating stage campaign: {str(e)}")
            progress_bar.progress(0)
            status_text.text("Stage campaign generation failed.")


def display_individual_journey_builder():
    """Display individual journey builder screen."""
    # Check if we're in funnel journey mode
    if st.session_state.get('funnel_journey_mode', False):
        display_funnel_journey_builder()
    else:
        # Display the Journey Builder interface similar to the main Journey Builder module
        display_funnel_journey_builder_interface()


def display_funnel_journey_builder_interface():
    """Display the Journey Builder interface in Funnel View with template-based generation."""
    st.write("## 🛠️ Journey Builder")
    st.write("Build personalized email journeys for your users.")

    # Load feature toggles
    from utils.file_utils import load_feature_toggles
    feature_toggles = load_feature_toggles()
    hide_csv_generation = feature_toggles.get('hide_behaviour_generation', False)

    # CSV Generation button (if not hidden)
    if not hide_csv_generation:
        col1, col2 = st.columns([3, 1])
        with col2:
            if st.button("📊 Generate User Behaviour", help="Generate user behaviour data for current organization"):
                from ui.journey import generate_organization_csv
                generate_organization_csv()

    # Load user journey stages
    from utils.file_utils import load_user_journey
    if 'user_journey' not in st.session_state:
        st.session_state.user_journey = [
            {'current_stage': 'New Visitor', 'goal_stage': 'Product Page Viewed'},
            {'current_stage': 'Product Page Viewed', 'goal_stage': 'Lead Generated'},
            {'current_stage': 'Lead Generated', 'goal_stage': 'Product Purchased'}
        ]

    # Define stage progression map
    stage_map = {stage['current_stage']: stage['goal_stage'] for stage in st.session_state.user_journey}

    # Check if organization products visibility is enabled
    org_filter_enabled = st.session_state.feature_toggles.get('org_products_visibility', True)

    # Get organization URL from session state if available
    org_url = None
    if hasattr(st.session_state, 'current_user') and st.session_state.current_user:
        org_url = st.session_state.current_user.get('organization', {}).get('url', None)

    # Load products with optional filtering
    from utils.file_utils import get_all_products
    all_products = get_all_products(organization_url=org_url, filter_by_org=org_filter_enabled)
    companies = set()

    # Extract company names
    for product in all_products:
        if product:
            companies.add(product.get('Company_Name', ''))

    print(f"Loaded {len(all_products)} products")

    if not all_products:
        if org_filter_enabled and org_url:
            st.error(f"No products found for your organization ({org_url}). Please add products first or disable organization filtering in settings.")
        else:
            st.error("Product details file not found or empty. Please add products first.")
        return

    if not companies:
        st.warning("No companies found in product details. Please add products first.")
        return

    # Initialize journey state in session if not exists
    if 'funnel_journey_generated' not in st.session_state:
        st.session_state.funnel_journey_generated = False

    # Only show input form if journey hasn't been generated
    if not st.session_state.funnel_journey_generated:
        # Load user data from CSV
        from ui.journey import load_user_data
        user_df, user_data_dict = load_user_data()

        # Add generation mode selection
        st.markdown("### 🎯 Generation Mode")
        generation_mode = st.selectbox(
            "Select Email Generation Mode",
            ["Template Based"],
            index=0,
            help="Template Based: Uses existing email templates with personalization based on user behavior and product features.",
            key="funnel_generation_mode_select"
        )

        # Store generation mode in session state
        st.session_state.funnel_generation_mode = "template"

        # Show mode description
        st.info("📝 **Template Based Mode**: Uses existing email templates with personalization based on user behavior and product features.")

        st.markdown("---")

        # User inputs
        col1, col2 = st.columns(2)

        with col1:
            selected_company = st.selectbox(
                "Select Company",
                sorted(list(companies)),
                key="funnel_company_select"
            )

            # User selection dropdown with user1/user2/user3 options
            if user_df is not None and not user_df.empty:
                # Get stage user details from funnel data
                org_name, csv_files, fallback_used = detect_organization_csv_files()
                funnel_data = load_funnel_data(csv_files)

                # Create user options from stage data
                user_options = ["Manual Entry"]
                stage_users = []

                # Collect users from all stages (up to 3 per stage)
                for stage_name, stage_df in funnel_data.items():
                    if not stage_df.empty:
                        stage_users_subset = stage_df.head(3)  # Get up to 3 users per stage
                        for idx, user_row in stage_users_subset.iterrows():
                            user_label = f"User {len(stage_users) + 1}"
                            user_options.append(user_label)
                            stage_users.append({
                                'label': user_label,
                                'first_name': user_row.get('first_name', 'User'),
                                'user_behaviour': user_row.get('user_behaviour', 'No behavior data'),
                                'user_stage': user_row.get('user_stage', 'New Visitor'),
                                'user_email': user_row.get('user_email', '')
                            })

                selected_user_option = st.selectbox(
                    "Select User",
                    user_options,
                    index=0,
                    key="funnel_user_selector"
                )

                # Initialize session state variables if they don't exist
                if 'funnel_auto_filled_name' not in st.session_state:
                    st.session_state.funnel_auto_filled_name = ""
                if 'funnel_auto_filled_behavior' not in st.session_state:
                    st.session_state.funnel_auto_filled_behavior = ""
                if 'funnel_auto_filled_stage' not in st.session_state:
                    st.session_state.funnel_auto_filled_stage = ""

                # Check if user selection has changed
                previous_selection = st.session_state.get('funnel_selected_user_option', 'Manual Entry')
                if selected_user_option != previous_selection:
                    # Store the current selection
                    st.session_state.funnel_selected_user_option = selected_user_option

                    if selected_user_option == "Manual Entry":
                        # Clear auto-filled values for manual entry
                        st.session_state.funnel_auto_filled_name = ""
                        st.session_state.funnel_auto_filled_behavior = ""
                        st.session_state.funnel_auto_filled_stage = ""
                    else:
                        # Find the selected user data
                        selected_user_data = None
                        for user_data in stage_users:
                            if user_data['label'] == selected_user_option:
                                selected_user_data = user_data
                                break

                        if selected_user_data:
                            # Update session state with selected user data
                            st.session_state.funnel_auto_filled_name = selected_user_data['first_name']
                            st.session_state.funnel_auto_filled_behavior = selected_user_data['user_behaviour']
                            st.session_state.funnel_auto_filled_stage = selected_user_data['user_stage']

                    # Force a rerun to update the form
                    st.rerun()

            # User name input field (always use the session state value)
            user_name = st.text_input(
                "User's First Name",
                value=st.session_state.get('funnel_auto_filled_name', ''),
                key="funnel_user_name_input"
            )
            # Always update the session state with the current value
            st.session_state.funnel_auto_filled_name = user_name

        with col2:
            # Get stages from user journey
            stages = [stage['current_stage'] for stage in st.session_state.user_journey]

            # Determine the default index for the stage dropdown
            default_stage_index = 0
            auto_filled_stage = st.session_state.get('funnel_auto_filled_stage', '')

            # Find the appropriate stage index
            if auto_filled_stage and auto_filled_stage in stages:
                default_stage_index = stages.index(auto_filled_stage)

            user_stage = st.selectbox(
                "User's Current Stage",
                stages,
                index=default_stage_index,
                key="funnel_user_stage_input"
            )

            # Update the auto-filled stage value if it was changed
            st.session_state.funnel_auto_filled_stage = user_stage

            # User behavior input field (always use the session state value)
            user_behavior = st.text_area(
                "User's Activity/Behavior",
                value=st.session_state.get('funnel_auto_filled_behavior', ''),
                placeholder="Describe the user's recent activity, interests, or behavior...",
                key="funnel_user_behavior_input"
            )
            # Always update the session state with the current value
            st.session_state.funnel_auto_filled_behavior = user_behavior

        # Store values in session state
        st.session_state.funnel_journey_user_behavior = user_behavior
        st.session_state.funnel_journey_user_stage = user_stage
        st.session_state.funnel_journey_user_name = user_name
        st.session_state.funnel_journey_company = selected_company

        if st.button("Generate Personalized Journey", type="primary", key="funnel_generate_journey"):
            if not user_name or not user_behavior:
                st.error("Please fill in all fields.")
                return

            generate_funnel_template_journey(selected_company, user_name, user_behavior, user_stage, all_products, stages)

    else:
        # Display the existing journey tree
        display_funnel_generated_journey()

        # Add button to generate new journey
        if st.button("Generate New Journey", key="funnel_new_journey"):
            st.session_state.funnel_journey_generated = False
            st.rerun()


def display_funnel_journey_builder():
    """Display journey builder for a specific user from funnel view."""
    user_row = st.session_state.get('funnel_journey_user', {})
    stage_name = st.session_state.get('funnel_journey_stage', '')

    st.write(f"## 🛠️ Journey Builder - {user_row.get('first_name', 'User')}")
    st.write(f"Building personalized email journey for user in **{stage_name.replace('_', ' ').title()}** stage")

    # Display user information
    col1, col2 = st.columns(2)
    with col1:
        st.markdown("**👤 User Details:**")
        st.markdown(f"- **Name:** {user_row.get('first_name', 'N/A')}")
        st.markdown(f"- **Email:** {user_row.get('user_email', 'N/A')}")
        st.markdown(f"- **Stage:** {user_row.get('user_stage', 'N/A')}")

    with col2:
        st.markdown("**🎯 User Behavior:**")
        behavior = user_row.get('user_behaviour', 'No behavior data available')
        st.markdown(f"_{behavior}_")

    # Back button
    if st.button("← Back to Stage Details", type="secondary"):
        st.session_state.funnel_journey_mode = False
        if 'funnel_journey_user' in st.session_state:
            del st.session_state.funnel_journey_user
        if 'funnel_journey_stage' in st.session_state:
            del st.session_state.funnel_journey_stage
        st.rerun()

    st.write("---")

    # Load products and find matching product for the user
    from utils.file_utils import get_all_products, load_user_journey
    from core.journey_builder import find_similar_product

    # Load products
    all_products = get_all_products()
    if not all_products:
        st.error("No products found. Please add products first.")
        return

    # Find matching product for user
    user_behavior = user_row.get('user_behaviour', '')
    matched_product, similarity = find_similar_product(user_behavior, all_products)

    if not matched_product:
        st.error("Could not find a matching product for this user.")
        return

    # Display matched product
    st.write("### 🎯 Matched Product")
    st.write(f"Based on the user's behavior, we recommend: **{matched_product.get('Product_Name')}**")
    st.write(f"Similarity Score: {similarity:.2f}")

    # Load user journey stages
    from utils.file_utils import load_user_journey
    stages = load_user_journey(matched_product.get('Product_Name'))
    if not stages:
        st.error("No user journey stages found. Please configure user journey first.")
        return

    # Find starting stage index
    current_stage = user_row.get('user_stage', stage_name)
    try:
        start_idx = next(i for i, stage in enumerate(stages) if stage.lower() == current_stage.lower())
    except StopIteration:
        start_idx = 0  # Default to first stage if not found

    # Generation mode selection
    st.write("### 🎯 Generation Mode")
    generation_mode = st.selectbox(
        "Select Email Generation Mode",
        ["Template Based", "Fully Personalized / End to End"],
        index=0,
        help="Template Based: Uses predefined templates with personalization. Fully Personalized: Creates completely custom emails without templates.",
        key="funnel_generation_mode"
    )

    # Convert generation mode for internal use
    generation_mode_internal = "template" if generation_mode == "Template Based" else "personalized"
    st.session_state.generation_mode = generation_mode_internal

    # Generate journey button
    if st.button("🚀 Generate Journey", type="primary", use_container_width=True):
        generate_funnel_user_journey(user_row, matched_product, stages, start_idx, generation_mode_internal)

    # Display existing journey if generated
    if st.session_state.get('journey_generated', False):
        display_generated_journey(stages, start_idx, matched_product)


def generate_funnel_template_journey(selected_company, user_name, user_behavior, user_stage, all_products, stages):
    """Generate template-based journey for funnel view."""
    import time
    from core.journey_builder import find_similar_product
    from core.email_generator import generate_tree_emails

    # Create a container for AI process visualization with a prominent header
    st.markdown("## 🧠 AI Decision-Making Process")
    st.markdown("### Watch the AI analyze behavior and create a personalized journey")

    # Add a divider for better visibility
    st.markdown("---")

    # Create collapsable sections for each AI process (defaulting to expanded state)

    # 1. User data analysis
    with st.expander("📃 **Analyzing User Data**", expanded=False):
        st.markdown("Collecting and processing user behavior data...")

        # Get organization_url for the selected company
        selected_company_products = [p for p in all_products if p.get('Company_Name') == selected_company]

        if not selected_company_products:
            st.error(f"No products found for {selected_company}")
            return

        # Get the organization_url from the first product
        org_url = selected_company_products[0].get('organization_url') or selected_company_products[0].get('Company_URL')

        if not org_url:
            st.error(f"No organization URL found for {selected_company}")
            return

        # Get all products with matching organization_url
        company_products = [p for p in all_products if (p.get('organization_url') == org_url or p.get('Company_URL') == org_url)]

        st.caption(f"Found {len(company_products)} products for {selected_company} using organization URL: {org_url}")

        if not company_products:
            st.error(f"No products found for organization URL: {org_url}")
            return

        # Small delay to make the process visible
        time.sleep(0.3)
        st.success("✓ User data analyzed successfully!")

    # 2. Product matching
    with st.expander("🎁 **Deciding the optimal product**", expanded=False):
        st.markdown("Using semantic analysis to match user behavior with the most relevant product...")
        time.sleep(0.5)

        # Find most similar product
        matched_product, similarity = find_similar_product(user_behavior, company_products)

        # Display product match results
        st.success("✓ Product matched successfully!")
        st.markdown(f"**Based on the user's activity, we recommend:** {matched_product.get('Product_Name')}")
        st.markdown(f"**Similarity Score:** {similarity:.2f}")

        # Display product features that might have influenced the match
        if 'Product_Features' in matched_product and matched_product['Product_Features']:
            st.write("**Key product features relevant to user behavior:**")
            for i, feature in enumerate(matched_product['Product_Features'][:3]):
                st.markdown(f"- {feature}")

    # Store the matched product in session state
    st.session_state.funnel_matched_product = matched_product

    # 3. Stage determination
    with st.expander("💼 **Deciding the next stages in the user journey as per current stage**", expanded=False):
        st.markdown(f"Analyzing user's current stage: **{user_stage}**")
        time.sleep(0.4)

        # Find starting stage index
        start_idx = stages.index(user_stage)
        remaining_stages = stages[start_idx:]

        # Show determined stages
        st.success("✓ User journey stages determined")
        st.markdown(f"**Current stage:** {user_stage}")
        if start_idx + 1 < len(stages):
            st.markdown(f"**Next target stage:** {stages[start_idx + 1]}")
        if start_idx + 2 < len(stages):
            st.markdown(f"**Future stage:** {stages[start_idx + 2]}")

    # 4. Email template selection
    with st.expander("📄 **Selecting email template for mail generation**", expanded=False):
        st.markdown("Choosing optimal email templates based on user stage and product...")
        time.sleep(0.4)

        # Show template selection result
        st.success("✓ Email templates selected")
        st.markdown("**Selected template categories:**")
        st.markdown("- Product introduction template")
        st.markdown("- Engagement follow-up template")
        st.markdown("- Conversion template")
        time.sleep(0.3)

    # 5. Email generation
    with st.expander("📧 **Generating personalised mail for stages**", expanded=False):
        st.markdown("Creating tailored email content for each stage using templates...")
        time.sleep(0.5)

        # Save journey state
        st.session_state.funnel_journey_generated = True
        st.session_state.funnel_journey_stages = stages
        st.session_state.funnel_current_stage_idx = start_idx
        st.session_state.funnel_tree_start_idx = start_idx

        # Set up session state for journey generation (similar to main journey builder)
        st.session_state.journey_user_name = user_name
        st.session_state.journey_user_behavior = user_behavior
        st.session_state.journey_user_stage = user_stage
        st.session_state.journey_company = selected_company
        st.session_state.matched_product = matched_product

        # Initialize journey steps
        st.session_state.journey_steps = []
        st.session_state.journey_steps.append({
            'type': 'stage',
            'action': user_stage,
            'timestamp': datetime.now().isoformat()
        })

        # Show progress bar for email generation
        progress_bar = st.progress(0)
        st.session_state.progress_bar = progress_bar

        # Generate emails with progress updates
        generate_tree_emails(user_stage, 0, 0, matched_product, progress_bar, start_idx, max_level=2, generation_mode="template")
        progress_bar.progress(100)

        st.success("✓ Template-based emails generated for all journey paths")
        st.markdown("**Email personalization strategies applied:**")
        st.markdown("- Dynamic product feature highlighting based on user behavior")
        st.markdown("- Stage-appropriate messaging and CTAs")
        st.markdown("- Personalized subject lines and preview text")

    # 6. Journey tree building
    with st.expander("🌳 **Building and plotting the journey tree for the user**", expanded=False):
        st.markdown("Creating visual representation of the user's journey...")
        time.sleep(0.4)

        st.success("✓ Journey tree created")
        st.markdown("**Tree structure:**")
        st.markdown("- Root node: Current user stage")
        st.markdown("- Left branches: Email opened/clicked but goal not achieved")
        st.markdown("- Right branches: Goal achieved, advancing to next stage")

    # Summary of AI work done
    st.success("🤖 **AI Process Complete:** User analyzed, optimal product matched, and personalized journey created!")
    st.write("Click on any node in the journey tree to view the personalized email content.")

    # Display the journey visualization elements
    st.write("### 🎯 Matched Product")
    st.write(f"Based on the user's activity, we recommend: **{matched_product.get('Product_Name')}**")
    st.write(f"Similarity Score: {similarity:.2f}")

    # Generate personalized emails for each stage
    st.write("### 📧 Personalized Email Journey")

    # Display the journey tree visualization
    st.write("### 🌳 Journey Tree with Emails")

    # Display the journey tree with email content side by side
    from core.journey_builder import create_journey_tree_plotly
    fig, node_ids = create_journey_tree_plotly(stages, start_idx, st.session_state.get("funnel_traversed_node", 1))

    # Create columns for tree and email
    tree_col, email_col = st.columns([2, 1])

    with tree_col:
        # Display interactive tree and capture clicks
        from streamlit_plotly_events import plotly_events
        clicked_data = plotly_events(fig, click_event=True, override_height=600, key="funnel_template_journey_tree")

        if clicked_data and isinstance(clicked_data, list) and len(clicked_data) > 0:
            clicked_item = clicked_data[0]
            node_number = clicked_item.get("pointNumber", 0)
            st.session_state["funnel_selected_node"] = node_number + 1
            st.session_state["funnel_traversed_node"] = node_number + 1

        # Add tree controls below the tree
        display_funnel_template_tree_controls(tree_col, start_idx, stages, all_products)

    with email_col:
        # Check if we have a selected node
        if "funnel_selected_node" in st.session_state:
            from core.journey_builder import handle_node_click
            handle_node_click(f"node_{st.session_state['funnel_selected_node']}")

    # Show success message
    st.success("Journey tree generated successfully! Click a node to view its email content.")

    # Store journey data in session state
    st.session_state.funnel_journey_generated = True
    st.session_state.funnel_journey_stages = stages
    st.session_state.funnel_current_stage_idx = start_idx


def display_funnel_generated_journey():
    """Display the existing journey tree for funnel view."""
    from core.journey_builder import create_journey_tree_plotly, handle_node_click, create_user_journey_flow
    from streamlit_plotly_events import plotly_events

    # Display the existing journey tree
    fig, node_ids = create_journey_tree_plotly(
        st.session_state.funnel_journey_stages,
        st.session_state.funnel_tree_start_idx,
        st.session_state.get("funnel_traversed_node", 1)
    )

    # Create columns for tree and email
    tree_col, email_col = st.columns([2, 1])

    with tree_col:
        # Display interactive tree and capture clicks
        clicked_data = plotly_events(fig, click_event=True, override_height=600, key="funnel_existing_journey_tree")

        if clicked_data and isinstance(clicked_data, list) and len(clicked_data) > 0:
            clicked_item = clicked_data[0]
            node_number = clicked_item.get("pointNumber", 0)
            st.session_state["funnel_selected_node"] = node_number + 1
            st.session_state["funnel_traversed_node"] = node_number + 1

        # Add tree controls below the tree
        display_funnel_template_tree_controls(
            tree_col,
            st.session_state.funnel_current_stage_idx,
            st.session_state.funnel_journey_stages,
            []
        )

    with email_col:
        # Check if we have a selected node
        if "funnel_selected_node" in st.session_state:
            handle_node_click(f"node_{st.session_state['funnel_selected_node']}")


def display_funnel_template_tree_controls(tree_col, current_stage_idx, stages=None, all_products=None):
    """Display tree traversal controls for the funnel template journey."""
    st.write("### Journey Controls")

    # Get the current node number based on stage
    current_node = st.session_state.get("funnel_traversed_node", 1)

    # Get stages from session state if not provided
    if stages is None and "funnel_journey_stages" in st.session_state:
        stages = st.session_state.funnel_journey_stages

    # Initialize journey steps if not exists
    if "journey_steps" not in st.session_state:
        st.session_state.journey_steps = []
        # Add initial stage
        if stages:
            st.session_state.journey_steps.append({
                'type': 'stage',
                'action': stages[current_stage_idx],
                'timestamp': datetime.now().isoformat()
            })

    # Get current stage based on current_stage_idx
    current_stage = stages[current_stage_idx] if stages else None

    # Create three columns for buttons
    left_col, middle_col, right_col = st.columns(3)

    with tree_col:
        # Display current stage
        st.write(f"**Current Stage:** {current_stage}")

    with left_col:
        # Email Open button - Allow at any stage except the last one
        if st.button("📧 Open Email", key="funnel_template_btn_open", disabled=current_stage_idx >= len(stages) - 1):
            st.session_state.journey_steps.append({
                'type': 'email',
                'action': 'Opened Email',
                'timestamp': datetime.now().isoformat()
            })
            st.rerun()

    with middle_col:
        # Email Click button - Allow at any stage except the last one
        if st.button("🖱️ Click Link", key="funnel_template_btn_click", disabled=current_stage_idx >= len(stages) - 1):
            st.session_state.journey_steps.append({
                'type': 'email',
                'action': 'Clicked Link',
                'timestamp': datetime.now().isoformat()
            })
            st.rerun()

    with right_col:
        # Next Stage button - Always leads to right child
        next_stage_name = None
        if stages:
            next_stage_name = stages[current_stage_idx + 1] if current_stage_idx + 1 < len(stages) else None

        if next_stage_name:
            if st.button(f"🎯 {next_stage_name}", key="funnel_template_btn_next"):
                st.session_state["funnel_current_stage_idx"] = current_stage_idx + 1
                st.session_state.journey_steps.append({
                    'type': 'stage',
                    'action': next_stage_name,
                    'timestamp': datetime.now().isoformat()
                })
                st.rerun()

    # Display user journey flow with refresh button
    journey_header_col, refresh_btn_col = st.columns([5, 1])
    with journey_header_col:
        st.write("### User Journey Flow")
    with refresh_btn_col:
        if st.button("🔄 Refresh Journey", key="funnel_template_btn_refresh"):
            # Initialize or reset the refresh journey state
            st.session_state.funnel_refresh_journey = True
            st.session_state.funnel_show_behavior_input = False
            st.rerun()

    new_behavior = None
    # Show behavior change dialog if refresh button was clicked
    if st.session_state.get('funnel_refresh_journey', False):
        behavior_changed = st.radio(
            "Has the user's behavior changed?",
            ["Yes", "No"],
            key="funnel_behavior_changed"
        )

        if behavior_changed == "Yes":
            # Show previous behavior
            st.write("**Previous User Behavior:**")
            st.info(st.session_state.get('funnel_journey_user_behavior', ''))

            # Get new behavior
            new_behavior = st.text_area(
                "Additional User Behavior",
                placeholder="Describe the user's new activity, interests, or behavior...",
                key="funnel_new_user_behavior"
            )

        if st.button("Update Journey", key="funnel_btn_update_journey"):
            # Update the user behavior by combining old and new
            if new_behavior:
                combined_behavior = f"{st.session_state.get('funnel_journey_user_behavior', '')}\n\nNew Behavior:\n{new_behavior}"
                st.session_state.funnel_journey_user_behavior = combined_behavior
                st.session_state.journey_user_behavior = combined_behavior
            else:
                combined_behavior = st.session_state.get('funnel_journey_user_behavior', '')

            # Reset journey from current stage
            current_stage = stages[current_stage_idx] if stages else None
            if current_stage:
                # Store only the steps up to current stage
                st.session_state.journey_steps = [
                    step for step in st.session_state.journey_steps
                    if step.get('type') == 'stage' and
                    stages.index(step.get('action', '')) <= current_stage_idx
                ]

                with st.spinner("Analyzing user behavior and generating personalized journey..."):
                    # Generate personalized emails for each stage
                    st.write("### 📧 Personalized Email Journey")

                    # Find starting stage index
                    start_idx = stages.index(current_stage)
                    remaining_stages = stages[start_idx:]

                    # Save journey state
                    st.session_state.funnel_journey_generated = True
                    st.session_state.funnel_journey_stages = stages
                    st.session_state.funnel_current_stage_idx = start_idx
                    st.session_state.funnel_tree_start_idx = start_idx

                    # Generate emails
                    st.write("### 🌳 Journey Tree with Emails")
                    st.write("Generating personalized emails for each node...")

                    progress_bar = st.progress(0)
                    st.session_state.progress_bar = progress_bar
                    from core.email_generator import generate_tree_emails
                    generate_tree_emails(current_stage, 0, 0, st.session_state.funnel_matched_product, progress_bar, start_idx, max_level=2, generation_mode="template")
                    progress_bar.progress(100)

                    # Store journey data in session state
                    st.session_state.funnel_journey_generated = True
                    st.session_state.funnel_refresh_journey = False
                    st.session_state.funnel_journey_stages = stages
                    st.session_state.funnel_current_stage_idx = start_idx
                    st.rerun()

    # Display journey visualization
    from core.journey_builder import create_user_journey_flow
    journey_fig = create_user_journey_flow(st.session_state.journey_steps)
    if journey_fig:
        st.plotly_chart(journey_fig, use_container_width=True)


def generate_funnel_user_journey(user_row, matched_product, stages, start_idx, generation_mode):
    """Generate journey for a specific user from funnel."""
    from core.email_generator import generate_tree_emails
    import time

    # Set up session state for journey generation
    st.session_state.journey_user_name = user_row.get('first_name', 'User')
    st.session_state.journey_user_email = user_row.get('user_email', '')
    st.session_state.journey_user_behavior = user_row.get('user_behaviour', 'No behavior data')
    st.session_state.journey_user_stage = user_row.get('user_stage', stages[start_idx] if start_idx < len(stages) else stages[0])
    st.session_state.matched_product = matched_product

    # Initialize journey steps
    st.session_state.journey_steps = []
    st.session_state.journey_steps.append({
        'type': 'stage',
        'action': stages[start_idx] if start_idx < len(stages) else stages[0],
        'timestamp': datetime.now().isoformat()
    })

    current_stage = stages[start_idx] if start_idx < len(stages) else stages[0]

    if generation_mode == 'template':
        with st.expander("📧 **Generating personalized emails for journey stages**", expanded=True):
            st.markdown("Creating tailored email content for each stage using templates...")
            time.sleep(0.5)

            # Save journey state
            st.session_state.journey_generated = True
            st.session_state.journey_stages = stages
            st.session_state.current_stage_idx = start_idx
            st.session_state.tree_start_idx = start_idx

            # Show progress bar for email generation
            progress_bar = st.progress(0)
            st.session_state.progress_bar = progress_bar

            # Generate emails with progress updates
            generate_tree_emails(current_stage, 0, 0, matched_product, progress_bar, start_idx, max_level=2, generation_mode=generation_mode)
            progress_bar.progress(100)

            st.success("✓ Template-based emails generated for all journey paths")
            st.markdown("**Email personalization strategies applied:**")
            st.markdown("- Dynamic product feature highlighting based on user behavior")
            st.markdown("- Stage-appropriate messaging and CTAs")
            st.markdown("- Personalized subject lines and preview text")
    else:
        with st.expander("🤖 **Generating fully personalized emails using AI**", expanded=True):
            st.markdown("Creating completely custom email content using specialized AI agent...")
            time.sleep(0.5)

            # Save journey state
            st.session_state.journey_generated = True
            st.session_state.journey_stages = stages
            st.session_state.current_stage_idx = start_idx
            st.session_state.tree_start_idx = start_idx

            # Show progress bar for email generation
            progress_bar = st.progress(0)
            st.session_state.progress_bar = progress_bar

            # Generate emails with progress updates
            generate_tree_emails(current_stage, 0, 0, matched_product, progress_bar, start_idx, max_level=2, generation_mode=generation_mode)
            progress_bar.progress(100)

            st.success("✓ Fully personalized emails generated using AI")
            st.markdown("**AI personalization features:**")
            st.markdown("- Complete email customization without templates")
            st.markdown("- Advanced user behavior analysis")
            st.markdown("- Dynamic content generation")

    st.rerun()


def display_generated_journey(stages, start_idx, matched_product):
    """Display the generated journey tree and email content."""
    from streamlit_plotly_events import plotly_events
    from core.journey_builder import create_journey_tree_plotly, handle_node_click, create_user_journey_flow

    st.write("### 🌳 Journey Tree with Emails")
    st.write("Click on any node to view the personalized email content.")

    # Display the journey tree with email content side by side
    fig, node_ids = create_journey_tree_plotly(stages, start_idx, st.session_state.get("traversed_node", 1))

    # Create columns for tree and email
    tree_col, email_col = st.columns([2, 1])

    with tree_col:
        # Display interactive tree and capture clicks
        clicked_data = plotly_events(fig, click_event=True, override_height=600, key="funnel_journey_tree")

        if clicked_data and isinstance(clicked_data, list) and len(clicked_data) > 0:
            clicked_item = clicked_data[0]
            node_number = clicked_item.get("pointNumber", 0)
            st.session_state["selected_node"] = node_number + 1
            st.session_state["traversed_node"] = node_number + 1

        # Add tree controls below the tree
        display_funnel_tree_controls(tree_col, start_idx, stages)

    with email_col:
        # Check if we have a selected node
        if "selected_node" in st.session_state:
            handle_node_click(f"node_{st.session_state['selected_node']}")

    # Show success message
    st.success("Journey tree generated successfully! Click a node to view its email content.")

    # Display journey visualization
    journey_fig = create_user_journey_flow(st.session_state.journey_steps)
    if journey_fig:
        st.write("### 📊 User Journey Flow")
        st.plotly_chart(journey_fig, use_container_width=True)


def display_funnel_tree_controls(tree_col, current_stage_idx, stages=None):
    """Display tree traversal controls for the funnel journey."""
    st.write("### Journey Controls")

    # Get the current node number based on stage
    current_node = st.session_state.get("traversed_node", 1)

    # Get stages from session state if not provided
    if stages is None and "journey_stages" in st.session_state:
        stages = st.session_state.journey_stages

    # Initialize journey steps if not exists
    if "journey_steps" not in st.session_state:
        st.session_state.journey_steps = []
        # Add initial stage
        if stages:
            st.session_state.journey_steps.append({
                'type': 'stage',
                'action': stages[current_stage_idx],
                'timestamp': datetime.now().isoformat()
            })

    # Get current stage based on current_stage_idx
    current_stage = stages[current_stage_idx] if stages else None

    # Create three columns for buttons
    left_col, middle_col, right_col = st.columns(3)

    with tree_col:
        # Display current stage
        st.write(f"**Current Stage:** {current_stage}")

    with left_col:
        # Email Open button - Allow at any stage except the last one
        if st.button("📧 Open Email", key="funnel_btn_open", disabled=current_stage_idx >= len(stages) - 1):
            st.session_state.journey_steps.append({
                'type': 'email',
                'action': 'Opened Email',
                'timestamp': datetime.now().isoformat()
            })
            st.rerun()

    with middle_col:
        # Email Click button - Allow at any stage except the last one
        if st.button("🖱️ Click Email", key="funnel_btn_click", disabled=current_stage_idx >= len(stages) - 1):
            st.session_state.journey_steps.append({
                'type': 'email',
                'action': 'Clicked Email',
                'timestamp': datetime.now().isoformat()
            })
            st.rerun()

    with right_col:
        # Next Stage button - Always leads to right child
        next_stage_name = None
        if stages:
            next_stage_name = stages[current_stage_idx + 1] if current_stage_idx + 1 < len(stages) else None

        if next_stage_name:
            if st.button(f"🎯 {next_stage_name}", key="funnel_btn_next"):
                st.session_state["current_stage_idx"] = current_stage_idx + 1
                st.session_state.journey_steps.append({
                    'type': 'stage',
                    'action': next_stage_name,
                    'timestamp': datetime.now().isoformat()
                })
                st.rerun()
