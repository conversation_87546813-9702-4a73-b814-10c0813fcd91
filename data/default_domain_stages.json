{"EdTech": [{"s_no": 1, "current_stage": "New Visitor", "description": "User is not aware of the product", "goal_stage": "Product Page Viewed"}, {"s_no": 2, "current_stage": "Product Page Viewed", "description": "User has viewed product details", "goal_stage": "Product Lead Generated"}, {"s_no": 3, "current_stage": "Product Lead Generated", "description": "User has shown interest in the product", "goal_stage": "Product Purchased"}, {"s_no": 4, "current_stage": "Product Purchased", "description": "User has completed the purchase", "goal_stage": "Thank You"}], "E-commerce": [{"s_no": 1, "current_stage": "Product Page Viewed", "description": "User has viewed product details", "goal_stage": "Added to <PERSON><PERSON>"}, {"s_no": 2, "current_stage": "Added to <PERSON><PERSON>", "description": "User has added the product to cart", "goal_stage": "Order Confirmation & Tracking"}, {"s_no": 3, "current_stage": "Order Confirmation & Tracking", "description": "User has purchased the product, we need to share the tracking details.", "goal_stage": "Order Shipped"}, {"s_no": 4, "current_stage": "Order Shipped", "description": "Order has been shipped to the user", "goal_stage": "Order Delivered"}, {"s_no": 5, "current_stage": "Order Delivered", "description": "Order has been delivered to the user", "goal_stage": "<PERSON><PERSON><PERSON>"}], "Insurance": [{"s_no": 1, "current_stage": "Lead Captured", "description": "User has shown initial interest in insurance plans and provided his details for the plan", "goal_stage": "Plan comparison"}, {"s_no": 2, "current_stage": "Plan comparison", "description": "User is exploring and comparing different insurance plans", "goal_stage": "Plan details viewed"}, {"s_no": 3, "current_stage": "Plan details viewed", "description": "User has viewed specific insurance plan details", "goal_stage": "Plan purchased"}, {"s_no": 4, "current_stage": "Plan purchased", "description": "User has purchased an insurance policy", "goal_stage": "Thank You & Policy Document Shared"}, {"s_no": 5, "current_stage": "Thank You & Policy Document Shared", "description": "User has received confirmation and policy documents", "goal_stage": "<PERSON><PERSON> / Upsell"}, {"s_no": 6, "current_stage": "<PERSON><PERSON> / Upsell", "description": "User is engaged for renewal or upsell opportunities", "goal_stage": "<PERSON><PERSON> / Upsell"}], "Banking": [{"s_no": 1, "current_stage": "Product Category Page Visited", "description": "User has visited the product category listing", "goal_stage": "Offering page visited"}, {"s_no": 2, "current_stage": "Offering page visited", "description": "User has explored a specific banking product offering", "goal_stage": "Application Submitted"}, {"s_no": 3, "current_stage": "Application Submitted", "description": "User has submitted an application for a product", "goal_stage": "KYC Completed"}, {"s_no": 4, "current_stage": "KYC Completed", "description": "User has completed identity verification (KYC)", "goal_stage": "Product Opted"}, {"s_no": 5, "current_stage": "Product Opted", "description": "User has successfully opted for the banking product", "goal_stage": "Thank You"}], "Quick-Commerce": [{"s_no": 1, "current_stage": "Download App/Visit Website", "description": "User has landed on the platform via app or website", "goal_stage": "Location Selected"}, {"s_no": 2, "current_stage": "Location Selected", "description": "User has selected their delivery location", "goal_stage": "Items Added to Cart"}, {"s_no": 3, "current_stage": "Items Added to Cart", "description": "User has added items to the shopping cart", "goal_stage": "Order Placed"}, {"s_no": 4, "current_stage": "Order Placed", "description": "User has confirmed and placed the order", "goal_stage": "Payment Completed"}, {"s_no": 5, "current_stage": "Payment Completed", "description": "User has successfully completed payment", "goal_stage": "Items Delivered"}, {"s_no": 6, "current_stage": "Items Delivered", "description": "User has received the ordered items, now ask the feedback on delivery and item quality", "goal_stage": "<PERSON><PERSON><PERSON>"}], "TravelTech": [{"s_no": 1, "current_stage": "Service Selected (Travel, Hotel, Package)", "description": "User has selected the type of service they are interested in", "goal_stage": "Service Detail Provided"}, {"s_no": 2, "current_stage": "Service Detail Provided", "description": "User has provided details about the service they are interested in(Venue, number of people etc)", "goal_stage": "Date Selected"}, {"s_no": 3, "current_stage": "Date Selected", "description": "User has selected preferred travel dates", "goal_stage": "Prices Compared"}, {"s_no": 4, "current_stage": "Prices Compared", "description": "User is comparing prices across services or providers", "goal_stage": "Booking Done"}, {"s_no": 5, "current_stage": "Booking Done", "description": "User has completed the booking process", "goal_stage": "Ticked Shared"}, {"s_no": 6, "current_stage": "Ticked Shared", "description": "User has received their travel ticket or booking confirmation", "goal_stage": "Upsell"}, {"s_no": 7, "current_stage": "Upsell", "description": "User is being targeted for additional related services", "goal_stage": "Upsell"}], "Creator Economy Platform": [{"s_no": 1, "current_stage": "Creator Viewed", "description": "User has viewed a creator's profile or page", "goal_stage": "Content Browsed"}, {"s_no": 2, "current_stage": "Content Browsed", "description": "User is browsing through the creator's content", "goal_stage": "Follow"}, {"s_no": 3, "current_stage": "Follow", "description": "User has followed or subscribed to the creator's updates", "goal_stage": "Engagement"}, {"s_no": 4, "current_stage": "Engagement", "description": "User is engaging with content (likes, comments, shares)", "goal_stage": "Paid Subscription / Purchase Item"}, {"s_no": 5, "current_stage": "Paid Subscription / Purchase Item", "description": "User has paid for a subscription or purchased content/items", "goal_stage": "Paid Subscription / Purchase Item"}], "B2B": [{"s_no": 1, "current_stage": "Product Viewed", "description": "User has viewed a product", "goal_stage": "Product Lead Generated"}, {"s_no": 2, "current_stage": "Product Lead Generated", "description": "User has generated a lead for the product", "goal_stage": "Business Discussion Started"}, {"s_no": 3, "current_stage": "Business Discussion Started", "description": "User has started a business discussion", "goal_stage": "Business Discussion Completed"}, {"s_no": 4, "current_stage": "Business Discussion Completed", "description": "User has completed a business discussion", "goal_stage": "Thank You"}]}